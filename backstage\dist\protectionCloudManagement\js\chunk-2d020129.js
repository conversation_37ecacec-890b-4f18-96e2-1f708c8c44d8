(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-2d020129"],{3644:function(t,e,a){var r=a("f037");(r="string"==typeof(r=r.__esModule?r.default:r)?[[t.i,r,""]]:r).locals&&(t.exports=r.locals);(0,a("499e").default)("60ff1ccd",r,!0,{sourceMap:!1,shadowMode:!1})},"5c71":function(t,e,a){a.r(e);a("7f7f"),a("57e7"),a("6d67");var r,n,l,s,i,o,c=a("2909"),d=(a("4f7f"),a("1c4c"),a("d25f"),a("5df3"),a("c7eb")),u=(a("96cf"),a("1da1")),h=(a("ac6a"),a("f3e2"),a("5530")),p=a("365c"),m=a("b2ab"),f=a("60bb"),b=a.n(f),f=a("5880"),h={name:"NewStandardDialog",computed:Object(h.a)({},Object(f.mapState)(["branch"])),data:function(){return{dialogForm:!1,loading:!1,formData:{},editableTabsValue:0,millConstructions:[],childMillConstructions:[],selectMillConstructions:[],selectChildMills:[],harmFactorInfo:[],cascaderProps:{label:"name",value:"_id",checkStrictly:!0,multiple:!1},childCascaderProps:{label:"name",value:"_id",checkStrictly:!0,multiple:!0},cascaderProps_protection:{label:"product",value:"product",children:"data"},productList:[],timeUnit:[{label:"天",value:"d"},{label:"周",value:"w"},{label:"月",value:"M"},{label:"季",value:"Q"},{label:"年",value:"y"}],categoryOptions:[{label:"普通",value:"normal"},{label:"特殊",value:"special"}],success:function(){},grantType:""}},methods:{transformCategoryTreeToProductList:function(t){var n=this,e=[];return this.processChildCategory=function(t,e){var a,r="".concat(e,"/").concat(t.name);return t.children&&0<t.children.length?(a={product:t.name,type:"category",data:[]},t.children.forEach(function(t){t=n.processChildCategory(t,r);t&&a.data.push(t)}),0<a.data.length?a:null):{product:t.name,categoryId:t._id,categoryPath:r,categoryName:t.name}},t.forEach(function(t){t=function(t){var e,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",r=a?"".concat(a,"/").concat(t.name):t.name;return t.children&&0<t.children.length?(e={product:t.name,type:"category",data:[]},t.children.forEach(function(t){t=n.processChildCategory(t,r);t&&e.data.push(t)}),0<e.data.length?e:void 0):{product:t.name,categoryId:t._id,categoryPath:r,categoryName:t.name}}(t);t&&e.push(t)}),e},loadProductList:(o=Object(u.a)(Object(d.a)().mark(function t(){var e;return Object(d.a)().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(m.e)({includeSystem:!0,activeOnly:!0});case 3:200===(e=t.sent).status&&(this.productList=this.transformCategoryTreeToProductList(e.data||[])),t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0);case 10:case"end":return t.stop()}},t,this,[[0,7]])})),function(){return o.apply(this,arguments)}),loadMillConstructions:(i=Object(u.a)(Object(d.a)().mark(function t(){var e;return Object(d.a)().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(p.k)();case 3:200===(e=t.sent).status&&(this.millConstructions=e.data||[]),t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0);case 10:case"end":return t.stop()}},t,this,[[0,7]])})),function(){return i.apply(this,arguments)}),loadInitialData:(s=Object(u.a)(Object(d.a)().mark(function t(){return Object(d.a)().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return this.loading=!0,t.prev=1,t.next=4,Promise.all([this.loadProductList(),this.loadMillConstructions()]);case 4:t.next=9;break;case 6:t.prev=6,t.t0=t.catch(1);case 9:return t.prev=9,this.loading=!1,t.finish(9);case 12:case"end":return t.stop()}},t,this,[[1,6,9,12]])})),function(){return s.apply(this,arguments)}),showModal:(l=Object(u.a)(Object(d.a)().mark(function t(e){return Object(d.a)().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.success?this.success=e.success:this.success=function(){},e.grantType&&(this.grantType=e.grantType),this.dialogForm=!0,t.next=5,this.loadInitialData();case 5:return t.next=7,this.initForm();case 7:this.editableTabsValue=0,e.EnterpriseID&&0<this.millConstructions.length&&(this.millConstructions=this.millConstructions.filter(function(t){return t.EnterpriseID===e.EnterpriseID})),this.handleEmployees(this.millConstructions),this.selectChildMills=[],this.childMillConstructions=[];case 12:case"end":return t.stop()}},t,this)})),function(t){return l.apply(this,arguments)}),open:(n=Object(u.a)(Object(d.a)().mark(function t(e){return Object(d.a)().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.showModal(e);case 2:case"end":return t.stop()}},t,this)})),function(t){return n.apply(this,arguments)}),initForm:function(){this.formData={products:[{product:"",productType:[],number:1,time:"",timeUnit:"M",maxNumber:""}],category:""},this.selectMillConstructions=[],this.harmFactorInfo=[]},handleChange:function(t){var s=this;this.$nextTick(function(){for(var t=s.$refs.myCascader.getCheckedNodes(),e=(s.$refs.myCascaderChild.handleClear(),s.$refs.myCascaderChild.panel.activePath=[],s.childMillConstructions=[],[]),a=(t[0]&&t[0].data&&t[0].data.children?s.childMillConstructions=b.a.cloneDeep(t[0].data.children):s.childMillConstructions=[],s.hanleSelectAllChildMill(b.a.cloneDeep(t[0].data),e,[]),s.selectChildMills=e,[]),r=0;r<t.length;r++){var n=t[r],l=[],n=(s.findHarmFactors(l,n),l=Array.from(new Set(l)),{label:n.label,harmFactors:l});a.push(n)}s.harmFactorInfo=a})},hanleSelectAllChildMill:function(t,e){var a=2<arguments.length&&void 0!==arguments[2]?arguments[2]:[];if(e.push(a.concat([t._id])),t.children)for(var r=a.concat([t._id]),n=0;n<t.children.length;n++){var l=t.children[n];this.hanleSelectAllChildMill(l,e,r)}},handleChildChange:function(t){},handleChangepProtection:function(t,e,a){a=this.$refs.protectionCascader[a].getCheckedNodes()[0];a.data&&a.data.categoryId?(e.categoryId=a.data.categoryId,e.categoryPath=a.data.categoryPath,e.categoryName=a.data.categoryName,e.product=a.data.categoryName):e.product=a.label},findHarmFactors:function(t,e){if(e.data.category||e.parent.data.harmFactors&&t.push.apply(t,Object(c.a)(e.parent.data.harmFactors.map(function(t){return t[1]}))),e.data.harmFactors&&t.push.apply(t,Object(c.a)(e.data.harmFactors.map(function(t){return t[1]}))),e.children)for(var a=0;a<e.children.length;a++)this.findHarmFactors(t,e.children[a])},add:function(){this.formData.products.push({product:"",productType:[],number:1,time:"",timeUnit:"M",maxNumber:""}),this.editableTabsValue=this.formData.products.length-1},removeTab:function(a){var r=this.formData.products,n=this.editableTabsValue;n===a&&r.forEach(function(t,e){e===parseInt(a)&&(e=r[e+1]||r[e-1])&&(n=r.indexOf(e))}),this.editableTabsValue=n,this.formData.products.splice(parseInt(a),1)},submit:(r=Object(u.a)(Object(d.a)().mark(function t(){var a,e,r,n=this;return Object(d.a)().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.selectMillConstructions&&0!==this.selectMillConstructions.length){t.next=3;break}return this.$message({message:"请先选择发放区域",type:"warning"}),t.abrupt("return");case 3:if(a="",this.formData.products.forEach(function(t,e){t.product&&t.number&&t.time&&t.timeUnit&&t.productType&&0!==t.productType.length||(a="请完善第".concat(e+1,"个防护用品的内容"))}),a)return this.$message({message:a,type:"warning"}),t.abrupt("return");t.next=8;break;case 8:return t.prev=8,JSON.parse(JSON.stringify(this.formData)),e=b.a.cloneDeep(this.selectMillConstructions).map(function(t,e){var t=n.findTargetMill(t,n.millConstructions),a="";return"mill"===n.grantType?a=t.category:"depart"===n.grantType&&(a=t.unitCode?"depart":"org"),{id:t._id,type:a,name:t.name}}),e={formData:this.formData,grantType:this.grantType,parentMills:e,childMills:this.selectChildMills},t.next=14,Object(p.a)({data:e,grantType:this.grantType});case 14:200!==(r=t.sent).status?t.next=19:(this.$message.success("新增标准成功"),t.next=20);break;case 19:throw new Error(r.message||"保存失败");case 20:this.$emit("save",{data:e,millConstructions:this.selectMillConstructions,childMills:this.selectChildMills,products:this.formData.products}),this.dialogForm=!1,t.next=27;break;case 24:t.prev=24,t.t0=t.catch(8),this.$message.error("新增标准失败："+t.t0.message);case 27:case"end":return t.stop()}},t,this,[[8,24]])})),function(){return r.apply(this,arguments)}),handleEmployees:function(t){for(var e=0;e<t.length;e++){var a=t[e];!(a.children&&0<a.children.length)||"stations"===a.category?delete a.children:this.handleEmployees(a.children),a.employees&&(a.name=a.employees.name,a._id=a.employees._id,delete a.children)}},findTargetMill:function(t,e){for(var a=0;a<e.length;a++){var r=e[a];if(r._id===t)return r;if(r.children&&0<r.children.length){r=this.findTargetMill(t,r.children);if(r)return r}}return null}}},f=(a("b005"),a("2877")),u=Object(f.a)(h,function(){var r=this,n=r._self._c;return n("div",[n("el-dialog",{staticClass:"dialog",attrs:{title:"新增配发标准",visible:r.dialogForm,width:"700px",top:"5vh","show-close":!1,"modal-append-to-body":!1}},[r.loading?n("div",{staticStyle:{padding:"20px"}},[n("el-skeleton",{attrs:{rows:8,animated:""}})],1):r.dialogForm?n("el-form",{attrs:{model:r.addPage,"label-position":"right","label-width":"85px"}},[n("el-form-item",{attrs:{label:"发放区域"}},[n("el-cascader",{ref:"myCascader",staticStyle:{width:"100%"},attrs:{options:r.millConstructions,props:r.cascaderProps},on:{change:r.handleChange},model:{value:r.selectMillConstructions,callback:function(t){r.selectMillConstructions=t},expression:"selectMillConstructions"}})],1),n("el-form-item",{attrs:{label:"危害因素"}},r._l(r.harmFactorInfo,function(t){return n("div",{key:t.label,staticClass:"harmFactorInfo"},[n("span",[r._v(r._s(t.label))]),r._v("：\n          "),n("span",[r._v(r._s(t.harmFactors.join("、")))]),t.harmFactors&&0!==t.harmFactors.length?r._e():n("span",[r._v("\n            暂无接触危害因素\n          ")])])}),0),n("el-form-item",{attrs:{label:"子区域"}},[n("el-cascader",{ref:"myCascaderChild",staticStyle:{width:"100%"},attrs:{options:r.childMillConstructions,props:r.childCascaderProps},on:{change:r.handleChildChange},model:{value:r.selectChildMills,callback:function(t){r.selectChildMills=t},expression:"selectChildMills"}})],1),"wh"===r.branch?n("el-form-item",{attrs:{label:"标签"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择标签"},model:{value:r.formData.category,callback:function(t){r.$set(r.formData,"category",t)},expression:"formData.category"}},r._l(r.categoryOptions,function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1):r._e(),n("el-divider",{attrs:{"content-position":"center"}},[r._v(" 防护用品 ")]),n("div",{staticClass:"tabs"},[n("el-button",{staticClass:"addBtn",attrs:{size:"mini"},on:{click:r.add}},[r._v("新增")]),n("el-tabs",{attrs:{type:"card",closable:""},on:{"tab-remove":r.removeTab},model:{value:r.editableTabsValue,callback:function(t){r.editableTabsValue=t},expression:"editableTabsValue"}},r._l(r.formData.products,function(e,a){return n("el-tab-pane",{key:a,attrs:{label:e.product,name:a}},[n("span",{attrs:{slot:"label"},slot:"label"},[r._v("\n              "+r._s(e.product||"请选择")+"\n            ")]),n("el-form-item",{attrs:{label:"防护用品"}},[n("el-cascader",{ref:"protectionCascader",refInFor:!0,staticStyle:{width:"100%"},attrs:{options:r.productList,props:r.cascaderProps_protection},on:{change:function(t){return r.handleChangepProtection(t,e,a)}},scopedSlots:r._u([{key:"default",fn:function(t){var e=t.node,t=t.data;return[n("span",[r._v(r._s(t.product||t.name||e.label||"未知"))])]}}],null,!0),model:{value:e.productType,callback:function(t){r.$set(e,"productType",t)},expression:"item.productType"}})],1),n("el-form-item",{attrs:{label:"数量"}},[n("el-input",{model:{value:e.number,callback:function(t){r.$set(e,"number",t)},expression:"item.number"}})],1),"wh"===r.branch?n("el-form-item",{attrs:{label:"首次发放最大允许量"}},[n("el-input",{model:{value:e.maxNumber,callback:function(t){r.$set(e,"maxNumber",t)},expression:"item.maxNumber"}})],1):r._e(),n("el-form-item",{attrs:{label:"领用周期"}},[n("el-input",{model:{value:e.time,callback:function(t){r.$set(e,"time",t)},expression:"item.time"}},[n("el-select",{staticStyle:{width:"100px"},attrs:{slot:"append",placeholder:"请选择"},slot:"append",model:{value:e.timeUnit,callback:function(t){r.$set(e,"timeUnit",t)},expression:"item.timeUnit"}},r._l(r.timeUnit,function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1)],1)],1)}),1)],1)],1):r._e(),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){r.dialogForm=!1}}},[r._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:r.submit}},[r._v("确定")])],1)],1)],1)},[],!1,null,"4fb805de",null);e.default=u.exports},b005:function(t,e,a){a("3644")},f037:function(t,e,a){(t.exports=a("2350")(!1)).push([t.i,".harmFactorInfo[data-v-4fb805de]{margin-bottom:10px;padding:8px;background-color:#f5f7fa;border-radius:4px;font-size:14px}.harmFactorInfo span[data-v-4fb805de]:first-child{font-weight:700;color:#606266}.tabs .addBtn[data-v-4fb805de]{margin-bottom:10px}.tabs .el-tabs .el-tab-pane[data-v-4fb805de]{padding:20px 0}",""])}}]);