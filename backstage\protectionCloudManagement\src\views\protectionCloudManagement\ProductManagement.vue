<template>
  <div class="product-management">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>防护用品产品管理</span>
      </div>

      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="left-actions">
          <!-- 分类筛选 -->
          <el-cascader
            v-model="selectedCategoryPath"
            :options="categoryTree"
            :props="categoryProps"
            placeholder="选择分类筛选"
            style="width: 200px; margin-right: 10px;"
            size="small"
            clearable
            @change="handleCategoryFilterChange"
          />
          
          <!-- 仓库筛选 -->
          <el-select
            v-model="queryParams.warehouseId"
            placeholder="选择仓库"
            style="width: 150px; margin-right: 10px;"
            size="small"
            clearable
            @change="handleWarehouseChange"
          >
            <el-option
              v-for="warehouse in warehouseList"
              :key="warehouse._id"
              :label="warehouse.name"
              :value="warehouse._id"
            />
          </el-select>

          <!-- 搜索框 -->
          <el-input
            v-model="queryParams.product"
            placeholder="搜索产品名称"
            style="width: 200px; margin-right: 10px;"
            size="small"
            clearable
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch" />
          </el-input>

          <!-- 状态筛选 -->
          <el-select
            v-model="queryParams.isActive"
            placeholder="产品状态"
            style="width: 120px; margin-right: 10px;"
            size="small"
            clearable
            @change="handleStatusFilterChange"
          >
            <el-option label="全部" :value="undefined"></el-option>
            <el-option label="启用" :value="true"></el-option>
            <el-option label="禁用" :value="false"></el-option>
          </el-select>
        </div>

        <div class="right-actions">
          <el-button size="small" @click="handleSmartImport">
            <i class="el-icon-upload2"></i> 智能导入
          </el-button>
          <el-button size="small" @click="handleTemplateConfig">
            <i class="el-icon-setting"></i> 模板配置
          </el-button>
          <el-button type="success" size="small" @click="handleAddProduct">
            <i class="el-icon-plus"></i> 新增产品
          </el-button>
        </div>
      </div>

      <!-- 产品列表表格 -->
      <el-table
        :data="productList"
        :loading="loading"
        stripe
        border
        :header-cell-style="{background:'#F5F7FA',color:'#4C91E9', 'font-weight':'normal'}"
        style="width: 100%;"
        @selection-change="handleSelectionChange"
        :scroll="{ x: 1800 }"
      >
        <el-table-column type="selection" width="55" />

        <!-- 图片列已隐藏 -->
        <!-- <el-table-column prop="picture" label="图片" width="100" align="center">
          <template slot-scope="scope">
            <img v-if="scope.row.picture" :src="scope.row.picture" style="width: 60px; height: 60px; object-fit: cover;" />
            <span v-else>-</span>
          </template>
        </el-table-column> -->

        <el-table-column prop="product" label="产品名称" min-width="150" />
        <el-table-column prop="materialCode" label="物料编码" width="120" />
        <el-table-column prop="modelNumber" label="型号" width="120" />
        <el-table-column prop="productSpec" label="产品规格" width="120" />
        <el-table-column prop="categoryName" label="分类" width="120" />

        <!-- 状态列 -->
        <el-table-column prop="isActive" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'" size="small">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="protectionType" label="防护类型" width="120" />
        <el-table-column prop="function" label="防护用途" width="120" />

        <!-- 危害因素列 -->
        <el-table-column prop="harmFactors" label="危害因素" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.harmFactors && scope.row.harmFactors.length > 0">
              {{ scope.row.harmFactors.join('、') }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="vender" label="厂家" width="120" />
        <el-table-column prop="surplus" label="库存" width="80" />

        <!-- 过期判断方式列 -->
        <el-table-column label="过期判断方式" width="140" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.hasExpiry">
              <el-tag type="success" size="mini">
                生产日期+{{ scope.row.expiryPeriod }}{{ getExpiryUnitText(scope.row.expiryUnit) }}
              </el-tag>
            </div>
            <div v-else>
              <el-tag type="primary" size="mini">配发标准周期</el-tag>
            </div>
          </template>
        </el-table-column>

        <!-- 特点列 -->
        <el-table-column prop="characteristic" label="特点" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.characteristic">{{ scope.row.characteristic }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 使用环境列 -->
        <el-table-column prop="industryEnvironment" label="使用环境" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.industryEnvironment">{{ scope.row.industryEnvironment }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <!-- 备注列 -->
        <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 自定义属性列 -->
        <el-table-column
          v-for="customField in displayCustomFields"
          :key="customField.key"
          :prop="`customAttributes.${customField.key}`"
          :label="customField.label"
          :width="customField.width || 120"
        >
          <template slot-scope="scope">
            {{ getCustomAttributeValue(scope.row, customField.key) }}
          </template>
        </el-table-column>

        <el-table-column prop="isActive" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>

    <!-- 产品编辑对话框 -->
    <ProductEditDialog
      :visible.sync="editDialogVisible"
      :product-data="currentProduct"
      :category-tree="categoryTree"
      :warehouse-list="warehouseList"
      @save="handleSaveProduct"
    />

    <!-- 智能导入对话框 -->
    <SmartImportDialog
      :visible.sync="importDialogVisible"
      :category-tree="categoryTree"
      :default-warehouse-id="queryParams.warehouseId"
      @import-success="handleImportSuccess"
    />

    <!-- 模板配置对话框 -->
    <TemplateConfigDialog
      :visible.sync="templateDialogVisible"
      :category-tree="categoryTree"
    />
  </div>
</template>

<script>
import {
  getProductList,
  saveProduct,
  deleteProducts,
  getWarehouseList
} from '@/api/index';
import { getProtectionCategoryTree } from '@/api/protectionCategory';
import ProductEditDialog from './components/ProductEditDialog.vue';
import SmartImportDialog from './components/SmartImportDialog.vue';
import TemplateConfigDialog from './components/TemplateConfigDialog.vue';

export default {
  name: 'ProductManagement',
  components: {
    ProductEditDialog,
    SmartImportDialog,
    TemplateConfigDialog
  },
  data() {
    return {
      loading: false,
      productList: [],
      total: 0,
      selectedProducts: [],
      
      // 查询参数
      queryParams: {
        page: 1,
        limit: 20,
        warehouseId: '',
        categoryId: '',
        product: ''
        // 移除 isActive 参数，显示所有状态的产品（启用+禁用）
      },

      // 分类相关
      categoryTree: [],
      selectedCategoryPath: [],
      categoryProps: {
        value: '_id',
        label: 'name',
        children: 'children',
        checkStrictly: true
      },

      // 仓库相关
      warehouseList: [],

      // 自定义字段显示
      displayCustomFields: [],

      // 对话框状态
      editDialogVisible: false,
      importDialogVisible: false,
      templateDialogVisible: false,
      currentProduct: null
    };
  },

  created() {
    this.loadCategoryTree();
    this.loadWarehouseList();
    this.loadProductList();
  },

  methods: {
    // 加载分类树
    async loadCategoryTree() {
      try {
        const response = await getProtectionCategoryTree();
        console.log('分类树响应:', response);
        if (response.status === 200) {
          this.categoryTree = response.data;
          console.log('分类树数据:', this.categoryTree);
        }
      } catch (error) {
        console.error('加载分类树错误:', error);
        this.$message.error('加载分类树失败: ' + error.message);
      }
    },

    // 加载仓库列表
    async loadWarehouseList() {
      try {
        const response = await getWarehouseList();
        console.log('仓库列表响应:', response);
        if (response.status === 200) {
          this.warehouseList = response.data.list;
          console.log('仓库列表数据:', this.warehouseList);

          // 设置默认仓库为公共仓库
          const publicWarehouse = this.warehouseList.find(w => w.isPublic);
          if (publicWarehouse && !this.queryParams.warehouseId) {
            this.queryParams.warehouseId = publicWarehouse._id;
          }
        }
      } catch (error) {
        console.error('加载仓库列表错误:', error);
        this.$message.error('加载仓库列表失败: ' + error.message);
      }
    },

    // 加载产品列表
    async loadProductList() {
      this.loading = true;
      try {
        const response = await getProductList(this.queryParams);
        console.log('产品列表响应:', response);
        if (response.status === 200) {
          this.productList = response.data.list;
          this.total = response.data.total;

          // 更新自定义字段显示
          this.updateDisplayCustomFields();
        }
      } catch (error) {
        console.error('加载产品列表错误:', error);
        this.$message.error('加载产品列表失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    },

    // 更新自定义字段显示
    updateDisplayCustomFields() {
      const customFieldsSet = new Set();
      
      this.productList.forEach(product => {
        if (product.customAttributes) {
          product.customAttributes.forEach(attr => {
            customFieldsSet.add(JSON.stringify({
              key: attr.key,
              label: attr.label
            }));
          });
        }
      });

      this.displayCustomFields = Array.from(customFieldsSet).map(fieldStr => {
        return JSON.parse(fieldStr);
      });
    },

    // 获取自定义属性值
    getCustomAttributeValue(product, key) {
      if (!product.customAttributes) return '';
      const attr = product.customAttributes.find(a => a.key === key);
      return attr ? attr.value : '';
    },

    // 获取有效期单位文本
    getExpiryUnitText(unit) {
      const unitMap = {
        'days': '天',
        'months': '月',
        'years': '年'
      };
      return unitMap[unit] || unit;
    },

    // 分类筛选变化
    handleCategoryFilterChange(value) {
      this.queryParams.categoryId = value && value.length > 0 ? value[value.length - 1] : '';
      this.queryParams.page = 1;
      this.loadProductList();
    },

    // 仓库变化
    handleWarehouseChange() {
      this.queryParams.page = 1;
      this.loadProductList();
    },

    // 搜索
    handleSearch() {
      this.queryParams.page = 1;
      this.loadProductList();
    },

    // 状态筛选变化
    handleStatusFilterChange() {
      this.queryParams.page = 1;
      this.loadProductList();
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.limit = val;
      this.queryParams.page = 1;
      this.loadProductList();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.queryParams.page = val;
      this.loadProductList();
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedProducts = selection;
    },

    // 新增产品
    handleAddProduct() {
      this.currentProduct = null;
      this.editDialogVisible = true;
    },

    // 编辑产品
    handleEdit(product) {
      try {
        // 深拷贝产品数据，确保自定义属性数组的独立性
        this.currentProduct = JSON.parse(JSON.stringify(product));
      } catch (error) {
        console.warn('ProductManagement - 产品数据深拷贝失败，使用浅拷贝:', error);
        this.currentProduct = { ...product };
      }
      this.editDialogVisible = true;
    },

    // 删除产品
    async handleDelete(product) {
      try {
        await this.$confirm('确定要删除这个产品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        const response = await deleteProducts({ ids: [product._id] });
        if (response.status === 200) {
          this.$message.success('删除成功');
          this.loadProductList();
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message);
        }
      }
    },

    // 保存产品
    async handleSaveProduct(productData) {
      try {
        const response = await saveProduct(productData);
        if (response.status === 200) {
          this.$message.success('保存成功');
          this.editDialogVisible = false;
          this.loadProductList();
        }
      } catch (error) {
        this.$message.error('保存失败: ' + error.message);
      }
    },

    // 智能导入
    handleSmartImport() {
      this.importDialogVisible = true;
    },

    // 导入成功
    handleImportSuccess() {
      this.loadProductList();
    },

    // 模板配置
    handleTemplateConfig() {
      this.templateDialogVisible = true;
    }
  }
};
</script>

<style scoped>
.product-management {
  padding: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.left-actions {
  display: flex;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 表格样式优化 */
.el-table {
  font-size: 12px;
}

.el-table .cell {
  padding: 0 8px;
  line-height: 1.4;
}

/* 图片样式 */
.el-table img {
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

/* 文本溢出处理 */
.el-table .el-tooltip {
  max-width: 200px;
}
</style>
