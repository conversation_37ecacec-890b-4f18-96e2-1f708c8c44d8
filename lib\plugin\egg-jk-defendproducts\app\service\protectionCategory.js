/*
 * @Author: 系统自动生成
 * @Date: 2024-07-24
 * @Description: 防护用品分类服务
 */

const { Service } = require('egg');

class ProtectionCategoryService extends Service {

  /**
   * 构建分类树
   * @param {Object} options - 选项
   * @param {String} options.enterpriseId - 企业ID
   * @param {Boolean} options.includeSystem - 是否包含系统默认分类
   * @param {Number} options.level - 限制层级
   * @param {Boolean} options.activeOnly - 仅活跃分类
   * @returns {Array} 分类树
   */
  async buildCategoryTree(options = {}) {
    const { ctx } = this;
    const {
      enterpriseId,
      includeSystem = false,
      level,
      activeOnly = true,
    } = options;

    const topEnterpriseId = enterpriseId || await ctx.helper.getTopEnterpriseId();

    // 构建查询条件
    const query = { topEnterpriseId };
    if (activeOnly) {
      query.isActive = true;
    }
    if (level) {
      query.level = { $lte: level };
    }
    if (includeSystem) {
      query.$or = [
        { topEnterpriseId },
        { isSystemDefault: true },
      ];
    }

    // 获取所有分类
    const categories = await ctx.service.db.find('ProtectionCategory', query, null, {
      sort: { level: 1, sort: 1, createdAt: 1 },
      lean: true, // 返回纯JSON对象，不是Mongoose文档
    });

    // 检查数据完整性
    const validCategories = categories.filter(category => {
      return category.name && typeof category.level === 'number';
    });

    // 构建树形结构
    return this._buildTree(validCategories);
  }

  /**
   * 获取分类路径
   * @param {String} categoryId - 分类ID
   * @returns {String} 分类路径
   */
  async getCategoryPath(categoryId) {
    const { ctx } = this;
    const category = await ctx.service.db.findOne('ProtectionCategory', { _id: categoryId });
    return category ? category.path : '';
  }

  /**
   * 验证分类层级
   * @param {String} parentId - 父级ID
   * @param {Number} level - 层级
   * @returns {Boolean} 是否有效
   */
  async validateCategoryLevel(parentId, level) {
    const { ctx } = this;

    if (!parentId) {
      return level === 1;
    }

    const parent = await ctx.service.db.findOne('ProtectionCategory', { _id: parentId });
    return parent && parent.level === level - 1;
  }

  /**
   * 智能推荐分类
   * @param {Array} harmFactors - 危害因素
   * @param {Object} context - 上下文信息
   * @returns {Array} 推荐的分类
   */
  async recommendCategories(harmFactors, context = {}) {
    const { ctx } = this;
    const topEnterpriseId = await ctx.helper.getTopEnterpriseId();
    console.log(context);
    if (!harmFactors || harmFactors.length === 0) {
      return [];
    }

    // 基于危害因素匹配分类
    const query = {
      topEnterpriseId,
      isActive: true,
      isLeaf: true, // 只推荐叶子节点
      harmFactors: { $in: harmFactors },
    };

    const categories = await ctx.service.db.find('ProtectionCategory', query, null, {
      sort: { sort: 1, createdAt: 1 },
      lean: true,
    });

    // 计算匹配度并排序
    return categories.map(category => {
      const matchCount = category.harmFactors.filter(factor =>
        harmFactors.includes(factor)
      ).length;
      const matchRate = matchCount / harmFactors.length;

      return {
        ...category,
        matchRate,
        matchCount,
      };
    }).sort((a, b) => b.matchRate - a.matchRate);
  }

  /**
   * 批量操作
   * @param {String} action - 操作类型 (enable/disable/delete)
   * @param {Array} ids - 分类ID数组
   * @param {Object} data - 更新数据
   * @returns {Object} 操作结果
   */
  async batchOperation(action, ids, data = {}) {
    const { ctx } = this;
    const topEnterpriseId = await ctx.helper.getTopEnterpriseId();

    if (!ids || ids.length === 0) {
      throw new Error('请选择要操作的分类');
    }

    const query = {
      _id: { $in: ids },
      topEnterpriseId,
    };

    let updateData = {};
    let result = {};
    let updatedCategories = [];

    switch (action) {
      case 'enable':
        updateData = { isActive: true };
        result = await ctx.service.db.updateMany('ProtectionCategory', query, updateData);
        // 获取更新后的数据
        if (result.modifiedCount > 0) {
          updatedCategories = await ctx.service.db.find('ProtectionCategory', query);
        }
        break;

      case 'disable':
        updateData = { isActive: false };
        result = await ctx.service.db.updateMany('ProtectionCategory', query, updateData);
        // 获取更新后的数据
        if (result.modifiedCount > 0) {
          updatedCategories = await ctx.service.db.find('ProtectionCategory', query);
        }
        break;

      case 'delete': {
        // 检查是否有子分类
        const hasChildren = await ctx.service.db.findOne('ProtectionCategory', {
          parentId: { $in: ids },
          topEnterpriseId,
        });

        if (hasChildren) {
          throw new Error('无法删除包含子分类的分类');
        }

        // 检查是否被使用
        const usageCheck = await this.checkCategoryUsage(ids);
        if (usageCheck.isUsed) {
          throw new Error(`分类正在被使用，无法删除。使用情况：${usageCheck.usage.join('、')}`);
        }

        result = await ctx.service.db.deleteMany('ProtectionCategory', query);
        break;
      }

      case 'update':
        updateData = { ...data };
        result = await ctx.service.db.updateMany('ProtectionCategory', query, updateData);
        // 获取更新后的数据
        if (result.modifiedCount > 0) {
          updatedCategories = await ctx.service.db.find('ProtectionCategory', query);
        }
        break;

      default:
        throw new Error('不支持的操作类型');
    }

    return {
      success: true,
      affected: result.modifiedCount || result.deletedCount || 0,
      action,
      updatedCategories, // 返回更新后的数据
    };
  }

  /**
   * 检查分类使用情况
   * @param {String|Array} categoryIds - 分类ID或ID数组
   * @returns {Object} 使用情况
   */
  async checkCategoryUsage(categoryIds) {
    const { ctx } = this;
    const ids = Array.isArray(categoryIds) ? categoryIds : [ categoryIds ];
    const usage = [];
    let isUsed = false;

    // 检查防护用品清单中的使用
    const suppliesUsage = await ctx.service.db.findOne('ProtectiveSuppliesList', {
      'list.data.categoryId': { $in: ids },
    });

    if (suppliesUsage) {
      usage.push('防护用品清单');
      isUsed = true;
    }

    // 检查配发标准中的使用
    const planUsage = await ctx.service.db.findOne('ProtectionPlan', {
      'products.categoryId': { $in: ids },
    });

    if (planUsage) {
      usage.push('配发标准');
      isUsed = true;
    }

    return {
      isUsed,
      usage,
    };
  }

  /**
   * 获取叶子节点列表
   * @param {Object} options - 查询选项
   * @returns {Array} 叶子节点列表
   */
  async getLeafCategories(options = {}) {
    const { ctx } = this;
    const {
      parentId,
      harmFactors,
      search,
      activeOnly = true,
    } = options;

    const topEnterpriseId = await ctx.helper.getTopEnterpriseId();

    const query = {
      topEnterpriseId,
      isLeaf: true,
    };

    if (activeOnly) {
      query.isActive = true;
    }

    if (parentId) {
      query.path = new RegExp(`^${parentId}`);
    }

    if (harmFactors && harmFactors.length > 0) {
      query.harmFactors = { $in: harmFactors };
    }

    if (search) {
      query.$or = [
        { name: new RegExp(search, 'i') },
        { code: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
      ];
    }

    return await ctx.service.db.find('ProtectionCategory', query, null, {
      sort: { sort: 1, createdAt: 1 },
      lean: true,
    });
  }

  /**
   * 构建树形结构的私有方法
   * @param {Array} categories - 分类列表
   * @param {String} parentId - 父级ID
   * @returns {Array} 树形结构
   */
  _buildTree(categories, parentId = null) {
    const tree = [];

    categories.forEach(category => {
      if (category.parentId === parentId) {
        const children = this._buildTree(categories, category._id);
        const node = {
          ...category,
          children: children.length > 0 ? children : undefined,
        };
        tree.push(node);
      }
    });

    return tree;
  }

  /**
   * 创建分类
   * @param {Object} categoryData - 分类数据
   * @returns {Object} 创建的分类
   */
  async createCategory(categoryData) {
    const { ctx } = this;

    // 获取顶级企业ID
    let topEnterpriseId;
    try {
      topEnterpriseId = await ctx.helper.getTopEnterpriseId();
    } catch (error) {
      // 如果获取失败，使用测试默认值
      topEnterpriseId = 'TEST_ENTERPRISE';
    }

    // 如果有父级分类，验证父级分类是否存在
    if (categoryData.parentId) {
      const parent = await ctx.service.db.findOne('ProtectionCategory', { _id: categoryData.parentId });
      if (!parent) {
        throw new Error('父级分类不存在');
      }
    }

    const data = {
      ...categoryData,
      topEnterpriseId,
      // level字段将由模型的pre-save中间件自动设置
    };

    return await ctx.service.db.create('ProtectionCategory', data);
  }

  /**
   * 更新分类
   * @param {String} categoryId - 分类ID
   * @param {Object} updateData - 更新数据
   * @returns {Object} 更新结果
   */
  async updateCategory(categoryId, updateData) {
    const { ctx } = this;
    const topEnterpriseId = await ctx.helper.getTopEnterpriseId();

    const query = {
      _id: categoryId,
      topEnterpriseId,
    };

    const result = await ctx.service.db.updateOne('ProtectionCategory', query, updateData);

    // 如果更新成功，返回更新后的数据
    if (result.modifiedCount > 0) {
      const updatedCategory = await ctx.service.db.findOne('ProtectionCategory', query);
      return {
        ...result,
        updatedCategory,
      };
    }

    return result;
  }

  /**
   * 删除分类
   * @param {String} categoryId - 分类ID
   * @returns {Object} 删除结果
   */
  async deleteCategory(categoryId) {
    const { ctx } = this;
    const topEnterpriseId = await ctx.helper.getTopEnterpriseId();

    // 检查是否有子分类
    const hasChildren = await ctx.service.db.findOne('ProtectionCategory', {
      parentId: categoryId,
      topEnterpriseId,
    });

    if (hasChildren) {
      throw new Error('无法删除包含子分类的分类');
    }

    // 检查使用情况
    const usageCheck = await this.checkCategoryUsage(categoryId);
    if (usageCheck.isUsed) {
      throw new Error(`分类正在被使用，无法删除。使用情况：${usageCheck.usage.join('、')}`);
    }

    const query = {
      _id: categoryId,
      topEnterpriseId,
    };

    return await ctx.service.db.deleteOne('ProtectionCategory', query);
  }
}

module.exports = ProtectionCategoryService;
