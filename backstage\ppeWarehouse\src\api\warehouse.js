import request from '@root/publicMethods/request';

/**
 * 获取仓库列表
 */
export function getWarehouseList(params = {}) {
  return request({
    url: '/manage/warehouse/list',
    method: 'get',
    params,
  });
}

/**
 * 创建仓库
 */
export function createWarehouse(data) {
  return request({
    url: '/manage/warehouse/create',
    method: 'post',
    data,
  });
}

/**
 * 更新仓库
 */
export function updateWarehouse(data) {
  return request({
    url: '/manage/warehouse/update',
    method: 'post',
    data,
  });
}

/**
 * 删除仓库
 */
export function deleteWarehouse(data) {
  return request({
    url: '/manage/warehouse/delete',
    method: 'post',
    data,
  });
}

/**
 * 获取有权限的公司列表
 */
export function getCompanyList(params) {
  return request({
    url: '/manage/warehouse/companyList',
    method: 'get',
    params,
  });
}

/**
 * 根据公司ID获取工作场所（懒加载）
 */
export function getWorkplacesByCompany(companyId, params) {
  return request({
    url: '/manage/warehouse/workplaces',
    method: 'get',
    params: {
      companyId,
      ...params,
    },
  });
}

/**
 * 获取车间岗位树（保留兼容性）
 */
export function getMillConstructionTree(params) {
  return request({
    url: '/manage/warehouse/millConstructionTree',
    method: 'get',
    params,
  });
}

/**
 * 获取已被管理的fullId列表
 */
export function getManagedFullIds(params) {
  return request({
    url: '/manage/warehouse/managedFullIds',
    method: 'get',
    params,
  });
}

/**
 * 初始化公共仓库
 */
export function initPublicWarehouse(data) {
  return request({
    url: '/manage/warehouse/initPublicWarehouse',
    method: 'post',
    data,
  });
}
