/*
 * @Author: 系统自动生成
 * @Date: 2024-12-20
 * @Description: 防护用品产品管理服务
 */

const Service = require('egg').Service;

class ProtectiveProductService extends Service {

  // 获取产品列表（支持有效期筛选）
  async getProductList(params) {
    const { ctx } = this;
    const {
      page = 1,
      limit = 20,
      warehouseId,
      categoryId,
      product,
      isActive, // 移除默认值，允许查询所有状态的产品
      hasExpiry,
      expiryStatus, // 'all', 'expiring', 'expired'
    } = params;

    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    // 构建查询条件
    const query = {
      EnterpriseID,
    };

    // 只有明确传递 isActive 参数时才添加到查询条件
    if (isActive !== undefined) query.isActive = isActive;
    if (warehouseId) query.warehouseId = warehouseId;
    if (categoryId) query.categoryId = categoryId;
    if (product) query.product = new RegExp(product, 'i');
    if (hasExpiry !== undefined) query.hasExpiry = hasExpiry;

    try {
      const skip = (page - 1) * limit;

      // 查询产品列表
      const products = await ctx.model.ProtectiveProduct
        .find(query)
        .skip(skip)
        .limit(parseInt(limit))
        .sort({ createdAt: -1 });

      // 获取总数
      const total = await ctx.model.ProtectiveProduct.countDocuments(query);

      // 如果需要筛选有效期状态，进行额外处理
      let filteredProducts = products;
      if (expiryStatus && expiryStatus !== 'all') {
        filteredProducts = this.filterByExpiryStatus(products, expiryStatus);
      }

      return {
        list: filteredProducts,
        total,
        page: parseInt(page),
        limit: parseInt(limit),
      };
    } catch (error) {
      ctx.logger.error('获取产品列表失败:', error);
      throw new Error('获取产品列表失败');
    }
  }

  // 保存/更新产品信息（包含有效期配置）
  async saveProduct(params) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      const {
        _id,
        product,
        productSpec,
        modelNumber,
        categoryId,
        categoryPath,
        categoryName,
        warehouseId,
        surplus = 0,
        hasExpiry = false,
        expiryPeriod,
        expiryUnit = 'days',
        needProductionDate = false,
        ...otherFields
      } = params;

      // 验证必填字段
      if (!product) {
        throw new Error('产品名称不能为空');
      }
      if (!warehouseId) {
        throw new Error('仓库ID不能为空');
      }

      // 验证有效期配置
      if (hasExpiry && !expiryPeriod) {
        throw new Error('启用有效期时必须设置有效期长度');
      }

      const productData = {
        EnterpriseID,
        product,
        productSpec,
        modelNumber,
        categoryId,
        categoryPath,
        categoryName,
        warehouseId,
        surplus: parseInt(surplus),
        hasExpiry,
        expiryPeriod: hasExpiry ? parseInt(expiryPeriod) : undefined,
        expiryUnit: hasExpiry ? expiryUnit : undefined,
        needProductionDate,
        ...otherFields,
      };

      let result;
      if (_id && _id.trim() !== '') {
        // 更新产品
        result = await ctx.model.ProtectiveProduct.findByIdAndUpdate(
          _id,
          productData,
          { new: true }
        );
      } else {
        // 新增产品 - 确保不包含空的_id字段
        if (productData._id === '' || productData._id === null || productData._id === undefined) {
          delete productData._id;
        }
        result = await ctx.model.ProtectiveProduct.create(productData);
      }

      return result;
    } catch (error) {
      ctx.logger.error('保存产品失败:', error);
      throw new Error(error.message || '保存产品失败');
    }
  }

  // 批量更新产品有效期配置
  async batchUpdateExpiry(params) {
    const { ctx } = this;
    const { productIds, hasExpiry, expiryPeriod, expiryUnit, needProductionDate } = params;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      throw new Error('请选择要更新的产品');
    }

    try {
      const updateData = {
        hasExpiry,
        needProductionDate,
      };

      if (hasExpiry) {
        if (!expiryPeriod) {
          throw new Error('启用有效期时必须设置有效期长度');
        }
        updateData.expiryPeriod = parseInt(expiryPeriod);
        updateData.expiryUnit = expiryUnit || 'days';
      } else {
        updateData.expiryPeriod = undefined;
        updateData.expiryUnit = undefined;
      }

      const result = await ctx.model.ProtectiveProduct.updateMany(
        { _id: { $in: productIds } },
        updateData
      );

      return {
        modifiedCount: result.modifiedCount,
        message: `成功更新 ${result.modifiedCount} 个产品的有效期配置`,
      };
    } catch (error) {
      ctx.logger.error('批量更新有效期配置失败:', error);
      throw new Error(error.message || '批量更新有效期配置失败');
    }
  }

  // 根据有效期状态筛选产品
  // eslint-disable-next-line no-unused-vars
  filterByExpiryStatus(products, status) {
    // eslint-disable-next-line no-unused-vars
    const now = new Date();

    return products.filter(product => {
      if (!product.hasExpiry) return false;

      // 这里需要根据实际的申请记录来判断产品的到期状态
      // 暂时返回所有有有效期的产品
      return true;
    });
  }

  // 计算产品到期时间
  calculateExpiryDate(productionDate, expiryPeriod, expiryUnit) {
    if (!productionDate || !expiryPeriod || !expiryUnit) {
      return null;
    }

    const date = new Date(productionDate);

    switch (expiryUnit) {
      case 'days':
        date.setDate(date.getDate() + expiryPeriod);
        break;
      case 'months':
        date.setMonth(date.getMonth() + expiryPeriod);
        break;
      case 'years':
        date.setFullYear(date.getFullYear() + expiryPeriod);
        break;
      default:
        return null;
    }

    return date;
  }

  // 检查产品是否即将到期
  isProductExpiring(expiryDate, warningDays = 7) {
    if (!expiryDate) return false;

    const now = new Date();
    const warningDate = new Date();
    warningDate.setDate(now.getDate() + warningDays);

    return expiryDate <= warningDate && expiryDate > now;
  }

  // 检查产品是否已过期
  isProductExpired(expiryDate) {
    if (!expiryDate) return false;

    const now = new Date();
    return expiryDate <= now;
  }
}

module.exports = ProtectiveProductService;
