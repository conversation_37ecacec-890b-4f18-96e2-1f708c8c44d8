<template>
  <div class="p-5">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-5">
      <h2 class="m-0 text-gray-800 text-xl font-medium">防护用品仓库管理</h2>
      <div class="space-x-2.5">
        <el-button type="primary" @click="openCreateDialog" :loading="loading">
          <i class="el-icon-plus" />
          新增仓库
        </el-button>
      </div>
    </div>

    <!-- 仓库列表 -->
    <div>
      <!-- 骨架屏 -->
      <div v-if="loading" class="p-5">
        <!-- 表格骨架屏 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden bg-white">
          <!-- 表头骨架 -->
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <div class="flex space-x-4">
              <div class="h-4 bg-gray-300 rounded w-24 animate-pulse"></div>
              <div class="h-4 bg-gray-300 rounded w-20 animate-pulse"></div>
              <div class="h-4 bg-gray-300 rounded w-32 animate-pulse"></div>
              <div class="h-4 bg-gray-300 rounded w-24 animate-pulse"></div>
              <div class="h-4 bg-gray-300 rounded w-16 animate-pulse"></div>
            </div>
          </div>
          <!-- 表格行骨架 -->
          <div v-for="i in 6" :key="i" class="px-4 py-3 border-b border-gray-100 last:border-b-0">
            <div class="flex items-center space-x-4">
              <div class="h-4 bg-gray-200 rounded flex-1 max-w-[200px] animate-pulse"></div>
              <div class="h-6 bg-blue-100 rounded-full w-20 animate-pulse"></div>
              <div class="h-4 bg-gray-200 rounded flex-1 max-w-[150px] animate-pulse"></div>
              <div class="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
              <div class="flex space-x-2">
                <div class="h-6 bg-blue-200 rounded w-12 animate-pulse"></div>
                <div class="h-6 bg-red-200 rounded w-12 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div v-else class="mb-5">
        <el-table
          :data="warehouseList"
          border
          style="width: 100%"
          empty-text="暂无仓库数据"
          v-loading="tableLoading"
        >
          <el-table-column prop="name" label="仓库名称" min-width="200" show-overflow-tooltip />
          <el-table-column label="仓库类型" width="120">
            <template slot-scope="{ row }">
              <el-tag :type="row.isPublic ? 'info' : 'primary'" class="w-20 text-center">
                {{ row.isPublic ? '公共仓库' : '独立仓库' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="管理范围" min-width="200">
            <template slot-scope="{ row }">
              <span v-if="row.isPublic">{{ getPublicWarehouseScope(row) }}</span>
              <span v-else-if="row.managementScope && row.managementScope.length > 0">
                {{ formatManagementScope(row.managementScope) }}
              </span>
              <span v-else class="text-gray-500">无</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="180">
            <template slot-scope="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                v-if="!row.isPublic"
                type="primary"
                size="mini"
                @click="openEditDialog(row)"
                :loading="row.updating"
              >
                编辑
              </el-button>
              <el-button
                v-if="!row.isPublic"
                type="danger"
                size="mini"
                @click="confirmDelete(row)"
                :loading="row.deleting"
              >
                删除
              </el-button>
              <span v-if="row.isPublic" class="text-gray-500">系统仓库</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container" style="text-align: center; margin-top: 20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          />
        </div>
      </div>
    </div>

    <!-- 新增/编辑仓库对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="95%"
      top="2vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleDialogClose"
      custom-class="management-scope-dialog"
    >
      <el-form
        ref="warehouseForm"
        :model="warehouseForm"
        :rules="rules"
        label-width="100px"
        v-loading="formLoading"
      >
        <el-form-item label="仓库名称" prop="name">
          <el-input
            v-model="warehouseForm.name"
            placeholder="请输入仓库名称"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="管理范围" prop="managementScope">
          <!-- 使用新的管理范围选择器组件 -->
          <ManagementScopeSelector
            ref="managementScopeSelector"
            :value="warehouseForm.managementScope"
            :disabled="submitting"
            :current-warehouse-id="currentWarehouse ? currentWarehouse._id : null"
            :primary-company-id="currentWarehouse ? currentWarehouse.primaryCompanyId : null"
            @input="handleScopeChange"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="submitWarehouseForm" :loading="submitting">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="温馨提示"
      :visible.sync="deleteDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <div>
        <p><i class="el-icon-warning text-yellow-500 mr-2"></i>删除仓库，将删除该仓库中的所有数据，确定删除吗？</p>
        <p class="text-xs text-gray-500 mt-2 mb-0">（如要保留数据，请修改仓库的管理范围，不要删除仓库）</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="deleteWarehouse" :loading="deleting">
          确定删除
        </el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { Message } from 'element-ui';
import {
  getWarehouseList,
  createWarehouse,
  updateWarehouse,
  deleteWarehouse,
  initPublicWarehouse as initPublicWarehouseApi,
} from '@/api/warehouse';
import ManagementScopeSelector from '@/components/ManagementScopeSelector.vue';

export default {
  name: 'WarehouseManagement',

  components: {
    ManagementScopeSelector
  },

  data() {
    return {
      // 仓库列表数据
      warehouseList: [],
      loading: false,
      tableLoading: false,
      initLoading: false,
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },

      // 对话框相关
      dialogVisible: false,
      dialogTitle: '新增仓库',
      submitting: false,
      formLoading: false,
      currentWarehouse: null,

      // 删除确认对话框
      deleteDialogVisible: false,
      deleting: false,

      // 表单数据
      warehouseForm: {
        name: '',
        managementScope: []
      },

      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入仓库名称', trigger: 'blur' },
          { max: 30, message: '仓库名称不能超过30个字符', trigger: 'blur' }
        ],
        managementScope: [
          {
            validator: (_rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请选择管理范围'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ]
      }
    };
  },

  created() {
    // 延迟一点时间，让骨架屏更明显
    setTimeout(() => {
      this.fetchWarehouseList();
    }, 300);
  },

  methods: {
    // 获取仓库列表
    async fetchWarehouseList() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
        };

        const res = await getWarehouseList(params);
        if (res.status === 200 || res.code === 200) {
          // 适配新的数据格式
          if (res.data && res.data.list && res.data.pagination) {
            // 新格式：{ data: { list: [...], pagination: {...} } }
            this.warehouseList = (res.data.list || []).map(item => ({
              ...item,
              updating: false,
              deleting: false,
            }));

            this.pagination = {
              ...this.pagination,
              ...res.data.pagination,
            };
          } else {
            // 兼容旧格式：{ data: [...] }
            this.warehouseList = (res.data || []).map(item => ({
              ...item,
              updating: false,
              deleting: false,
            }));
          }
        } else {
          Message.error(res.message || '获取仓库列表失败');
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error);
        Message.error('获取仓库列表失败');
      } finally {
        this.loading = false;
      }
    },



    // 初始化公共仓库
    async initPublicWarehouse() {
      this.initLoading = true;
      try {
        const res = await initPublicWarehouseApi();
        if (res.status === 200 || res.code === 200) {
          Message.success('初始化公共仓库成功');
          await this.fetchWarehouseList();
        } else {
          Message.error(res.message || '初始化公共仓库失败');
        }
      } catch (error) {
        console.error('初始化公共仓库失败:', error);
        Message.error('初始化公共仓库失败');
      } finally {
        this.initLoading = false;
      }
    },

    // 打开创建对话框
    openCreateDialog() {
      this.dialogTitle = '新增仓库';
      this.warehouseForm = {
        name: '',
        managementScope: []
      };
      this.currentWarehouse = null;
      this.dialogVisible = true;

      // 等待弹窗打开后刷新管理范围选择器数据
      this.$nextTick(() => {
        if (this.$refs.managementScopeSelector) {
          this.$refs.managementScopeSelector.forceRefresh();
        }
      });
    },

    // 打开编辑对话框
    openEditDialog(warehouse) {
      this.dialogTitle = '编辑仓库';
      this.currentWarehouse = warehouse;
      this.warehouseForm = {
        name: warehouse.name,
        managementScope: warehouse.managementScope || []
      };
      this.dialogVisible = true;

      // 等待弹窗打开后刷新管理范围选择器数据
      this.$nextTick(() => {
        if (this.$refs.managementScopeSelector) {
          this.$refs.managementScopeSelector.forceRefresh();
        }
      });
    },

    // 管理范围变化事件
    handleScopeChange(managementScope) {
      this.warehouseForm.managementScope = managementScope;
      // 触发表单验证
      this.$refs.warehouseForm.validateField('managementScope');
    },



    // 提交表单
    submitWarehouseForm() {
      this.$refs.warehouseForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            let res;
            if (this.currentWarehouse) {
              // 编辑仓库
              res = await updateWarehouse({
                _id: this.currentWarehouse._id,
                ...this.warehouseForm
              });
            } else {
              // 创建仓库
              res = await createWarehouse(this.warehouseForm);
            }

            if (res.status === 200 || res.code === 200) {
              Message.success(this.currentWarehouse ? '编辑仓库成功' : '创建仓库成功');
              this.handleDialogClose();
              await this.fetchWarehouseList();
            } else {
              Message.error(res.message || '操作失败');
            }
          } catch (error) {
            console.error('操作失败:', error);
            Message.error(error.message || '操作失败');
          } finally {
            this.submitting = false;
          }
        }
      });
    },

    // 确认删除
    confirmDelete(warehouse) {
      this.currentWarehouse = warehouse;
      this.deleteDialogVisible = true;
    },

    // 删除仓库
    async deleteWarehouse() {
      this.deleting = true;
      try {
        const res = await deleteWarehouse({ warehouseId: this.currentWarehouse._id });
        if (res.status === 200 || res.code === 200) {
          Message.success('删除仓库成功');
          this.deleteDialogVisible = false;
          await this.fetchWarehouseList();
        } else {
          Message.error(res.message || '删除仓库失败');
        }
      } catch (error) {
        console.error('删除仓库失败:', error);
        Message.error(error.message || '删除仓库失败');
      } finally {
        this.deleting = false;
      }
    },

    // 关闭对话框
    handleDialogClose() {
      this.dialogVisible = false;
      if (this.$refs.warehouseForm) {
        this.$refs.warehouseForm.resetFields();
      }
      this.currentWarehouse = null;
    },

    // 获取公共仓库的管理范围显示
    getPublicWarehouseScope(warehouse) {
      // 公共仓库显示企业名称
      if (warehouse.enterpriseName) {
        return warehouse.enterpriseName;
      }
      // 如果没有企业名称，显示默认文本
      return '全部车间岗位';
    },

    // 格式化管理范围显示（用于表格显示）
    formatManagementScope(scope) {
      if (!scope || scope.length === 0) {
        return '无';
      }

      // 统计不同层级的数量
      const counts = {
        mill: 0,
        workspaces: 0,
        stations: 0
      };

      scope.forEach(item => {
        if (counts[item.level] !== undefined) {
          counts[item.level]++;
        }
      });

      const parts = [];
      if (counts.mill > 0) parts.push(`${counts.mill}个厂房`);
      if (counts.workspaces > 0) parts.push(`${counts.workspaces}个车间`);
      if (counts.stations > 0) parts.push(`${counts.stations}个岗位`);

      return parts.length > 0 ? parts.join('，') : `${scope.length}个范围`;
    },



    // 分页大小改变
    handleSizeChange(newSize) {
      this.pagination.pageSize = newSize;
      this.pagination.page = 1; // 重置到第一页
      this.fetchWarehouseList();
    },

    // 当前页改变
    handleCurrentChange(newPage) {
      this.pagination.page = newPage;
      this.fetchWarehouseList();
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    },
  },
};
</script>

<style scoped>
/* 对话框样式优化 - 固定高度 */
:deep(.management-scope-dialog) {
  height: 90vh !important;
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
}

:deep(.management-scope-dialog .el-dialog__body) {
  padding: 0;
  /* height: 600px !important; 固定高度 */
  overflow: hidden;
}

/* 表单容器样式 */
:deep(.el-form) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-form .el-form-item:last-child) {
  flex: 1;
  margin-bottom: 0;
}

:deep(.el-form .el-form-item:last-child .el-form-item__content) {
  height: 100%;
}

/* 管理范围选择器在对话框中的样式 - 固定高度 */
:deep(.management-scope-selector) {
  height: 600px !important; /* 固定高度 */
  background: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

:deep(.management-scope-selector .main-content) {
  height: 100%; /* 占满整个容器 */
  padding: 16px;
  gap: 16px;
  grid-template-columns: 320px 1fr 380px;
}

/* 面板卡片样式优化 */
:deep(.management-scope-selector .panel-card) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

:deep(.management-scope-selector .panel-card:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式优化 */
:deep(.tree-container::-webkit-scrollbar),
:deep(.selected-list::-webkit-scrollbar) {
  width: 6px;
}

:deep(.tree-container::-webkit-scrollbar-track),
:deep(.selected-list::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 3px;
}

:deep(.tree-container::-webkit-scrollbar-thumb),
:deep(.selected-list::-webkit-scrollbar-thumb) {
  background: #cbd5e1;
  border-radius: 3px;
}

:deep(.tree-container::-webkit-scrollbar-thumb:hover),
:deep(.selected-list::-webkit-scrollbar-thumb:hover) {
  background: #94a3b8;
}

/* 响应式布局优化 */
@media (max-width: 1600px) {
  :deep(.management-scope-selector .main-content) {
    grid-template-columns: 300px 1fr 360px;
  }
}

@media (max-width: 1400px) {
  :deep(.management-scope-selector .main-content) {
    grid-template-columns: 280px 1fr 340px;
  }
}

@media (max-width: 1200px) {
  :deep(.management-scope-dialog) {
    width: 98% !important;
    height: 90vh !important;
  }

  :deep(.management-scope-dialog .el-dialog__body) {
    height: 600px !important;
  }

  :deep(.management-scope-selector .main-content) {
    grid-template-columns: 1fr;
    gap: 12px;
    height: 520px; /* 保持固定高度 */
  }

  :deep(.panel-card) {
    height: 160px !important; /* 移动端降低面板高度 */
  }

  :deep(.company-list) {
    height: 100px !important;
  }

  :deep(.tree-container) {
    height: 100px !important;
  }

  :deep(.selected-list) {
    height: 60px !important;
  }
}

@media (max-width: 768px) {
  :deep(.management-scope-dialog) {
    width: 100% !important;
    height: 100vh !important;
    margin: 0 !important;
    top: 0 !important;
  }

  :deep(.management-scope-dialog .el-dialog__body) {
    height: calc(100vh - 100px) !important;
  }

  :deep(.management-scope-selector) {
    height: calc(100vh - 100px) !important;
  }

  :deep(.management-scope-selector .main-content) {
    height: calc(100vh - 180px) !important;
  }
}

/* 保留原有的自定义树形控件样式（用于兼容） */
:deep(.custom-tree .el-tree-node__content) {
  height: auto;
  padding: 8px 0;
}

:deep(.custom-tree .el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.custom-tree .el-tree-node__expand-icon) {
  color: #409eff;
}

:deep(.custom-tree .el-checkbox__input.is-disabled .el-checkbox__inner) {
  background-color: #f5f5f5;
  border-color: #d3d4d6;
}

:deep(.custom-tree .el-checkbox__input.is-disabled + .el-checkbox__label) {
  color: #c0c4cc;
}
</style>



